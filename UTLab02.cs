using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyApp;

namespace MyApp.Tests
{
    [TestClass]
    public class UTLab02
    {
        private MethodLibrary.MethodLibrary obj = new MethodLibrary.MethodLibrary();

        [TestMethod]
        public void Test1()
        {
            string result = obj.SolveQuadratic(0, 0, 0, out float x1, out float x2);

            Assert.AreEqual("Vô số nghiệm", result);
            Assert.IsTrue(float.IsNaN(x1));
            Assert.IsTrue(float.IsNaN(x2));
        }

        [TestMethod]
        public void Test2()
        {
            string result = obj.SolveQuadratic(1, 1, 1, out float x1, out float x2);

            Assert.AreEqual("Vô nghiệm", result);
            Assert.IsTrue(float.IsNaN(x1));
            Assert.IsTrue(float.IsNaN(x2));
        }

        [TestMethod]
        public void Test3()
        {
            string result = obj.SolveQuadratic(0, 2, 4, out float x1, out float x2);

            Assert.AreEqual("Có 1 nghiệm", result);
            Assert.AreEqual(-2f, x1);
            Assert.AreEqual(-2f, x2);
        }

        [TestMethod]
        public void Test4()
        {
            string result = obj.SolveQuadratic(1, -3, 2, out float x1, out float x2);

            Assert.AreEqual("Có 2 nghiệm phân biệt", result);
            Assert.AreEqual(1f, x1);
            Assert.AreEqual(2f, x2);
        }

        [TestMethod]
        public void Test5()
        {
            string result = obj.SolveQuadratic(1, 2, 1, out float x1, out float x2);

            Assert.AreEqual("Có nghiệm kép", result);
            Assert.AreEqual(-1f, x1);
            Assert.AreEqual(-1f, x2);
        }
    }
}