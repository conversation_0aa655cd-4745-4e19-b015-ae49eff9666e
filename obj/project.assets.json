{"version": 3, "targets": {"net9.0": {"Microsoft.ApplicationInsights/2.22.0": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}}, "Microsoft.CodeCoverage/17.12.0": {"type": "package", "compile": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "runtime": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "build": {"build/netstandard2.0/Microsoft.CodeCoverage.props": {}, "build/netstandard2.0/Microsoft.CodeCoverage.targets": {}}}, "Microsoft.NET.Test.Sdk/17.12.0": {"type": "package", "dependencies": {"Microsoft.CodeCoverage": "17.12.0", "Microsoft.TestPlatform.TestHost": "17.12.0"}, "compile": {"lib/netcoreapp3.1/_._": {}}, "runtime": {"lib/netcoreapp3.1/_._": {}}, "build": {"build/netcoreapp3.1/Microsoft.NET.Test.Sdk.props": {}, "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.NET.Test.Sdk.props": {}}}, "Microsoft.Testing.Extensions.Telemetry/1.4.3": {"type": "package", "dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Testing.Platform": "1.4.3"}, "compile": {"lib/net8.0/Microsoft.Testing.Extensions.Telemetry.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Testing.Extensions.Telemetry.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"buildTransitive/net8.0/Microsoft.Testing.Extensions.Telemetry.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Testing.Extensions.Telemetry.props": {}}}, "Microsoft.Testing.Extensions.TrxReport.Abstractions/1.4.3": {"type": "package", "dependencies": {"Microsoft.Testing.Platform": "1.4.3"}, "compile": {"lib/net8.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Testing.Extensions.VSTestBridge/1.4.3": {"type": "package", "dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.TestPlatform.ObjectModel": "17.11.1", "Microsoft.Testing.Extensions.Telemetry": "1.4.3", "Microsoft.Testing.Extensions.TrxReport.Abstractions": "1.4.3", "Microsoft.Testing.Platform": "1.4.3"}, "compile": {"lib/net8.0/Microsoft.Testing.Extensions.VSTestBridge.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Testing.Extensions.VSTestBridge.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Testing.Platform/1.4.3": {"type": "package", "compile": {"lib/net8.0/Microsoft.Testing.Platform.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Testing.Platform.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/cs/Microsoft.Testing.Platform.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.Testing.Platform.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Testing.Platform.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Testing.Platform.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Testing.Platform.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Testing.Platform.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Testing.Platform.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.Testing.Platform.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.Testing.Platform.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Testing.Platform.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.Testing.Platform.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.Testing.Platform.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Testing.Platform.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"buildTransitive/net8.0/Microsoft.Testing.Platform.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Testing.Platform.props": {}}}, "Microsoft.Testing.Platform.MSBuild/1.4.3": {"type": "package", "dependencies": {"Microsoft.Testing.Platform": "1.4.3"}, "compile": {"lib/net8.0/Microsoft.Testing.Platform.MSBuild.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Testing.Platform.MSBuild.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"buildTransitive/net8.0/Microsoft.Testing.Platform.MSBuild.props": {}, "buildTransitive/net8.0/Microsoft.Testing.Platform.MSBuild.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.props": {}, "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.targets": {}}}, "Microsoft.TestPlatform.ObjectModel/17.12.0": {"type": "package", "dependencies": {"System.Reflection.Metadata": "1.6.0"}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.TestHost/17.12.0": {"type": "package", "dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.12.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}, "lib/netcoreapp3.1/testhost.dll": {"related": ".deps.json"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}, "lib/netcoreapp3.1/testhost.dll": {"related": ".deps.json"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"build/netcoreapp3.1/Microsoft.TestPlatform.TestHost.props": {}}}, "MSTest/3.6.4": {"type": "package", "dependencies": {"MSTest.Analyzers": "[3.6.4]", "MSTest.TestAdapter": "[3.6.4]", "MSTest.TestFramework": "[3.6.4]", "Microsoft.NET.Test.Sdk": "17.11.1"}}, "MSTest.Analyzers/3.6.4": {"type": "package"}, "MSTest.TestAdapter/3.6.4": {"type": "package", "dependencies": {"Microsoft.Testing.Extensions.VSTestBridge": "1.4.3", "Microsoft.Testing.Platform.MSBuild": "1.4.3"}, "build": {"build/net8.0/MSTest.TestAdapter.props": {}, "build/net8.0/MSTest.TestAdapter.targets": {}}}, "MSTest.TestFramework/3.6.4": {"type": "package", "compile": {"lib/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"build/net8.0/MSTest.TestFramework.targets": {}}}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/5.0.0": {"type": "package", "compile": {"lib/net5.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}}, "System.Reflection.Metadata/1.6.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}}}}, "libraries": {"Microsoft.ApplicationInsights/2.22.0": {"sha512": "3AOM9bZtku7RQwHyMEY3tQMrHIgjcfRDa6YQpd/QG2LDGvMydSlL9Di+8LLMt7J2RDdfJ7/2jdYv6yHcMJAnNw==", "type": "package", "path": "microsoft.applicationinsights/2.22.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net452/Microsoft.ApplicationInsights.dll", "lib/net452/Microsoft.ApplicationInsights.pdb", "lib/net452/Microsoft.ApplicationInsights.xml", "lib/net46/Microsoft.ApplicationInsights.dll", "lib/net46/Microsoft.ApplicationInsights.pdb", "lib/net46/Microsoft.ApplicationInsights.xml", "lib/netstandard2.0/Microsoft.ApplicationInsights.dll", "lib/netstandard2.0/Microsoft.ApplicationInsights.pdb", "lib/netstandard2.0/Microsoft.ApplicationInsights.xml", "microsoft.applicationinsights.2.22.0.nupkg.sha512", "microsoft.applicationinsights.nuspec"]}, "Microsoft.CodeCoverage/17.12.0": {"sha512": "4svMznBd5JM21JIG2xZKGNanAHNXplxf/kQDFfLHXQ3OnpJkayRK/TjacFjA+EYmoyuNXHo/sOETEfcYtAzIrA==", "type": "package", "path": "microsoft.codecoverage/17.12.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.txt", "build/netstandard2.0/CodeCoverage/CodeCoverage.config", "build/netstandard2.0/CodeCoverage/CodeCoverage.exe", "build/netstandard2.0/CodeCoverage/Cov_x86.config", "build/netstandard2.0/CodeCoverage/amd64/CodeCoverage.exe", "build/netstandard2.0/CodeCoverage/amd64/Cov_x64.config", "build/netstandard2.0/CodeCoverage/amd64/covrun64.dll", "build/netstandard2.0/CodeCoverage/amd64/msdia140.dll", "build/netstandard2.0/CodeCoverage/arm64/Cov_arm64.config", "build/netstandard2.0/CodeCoverage/arm64/covrunarm64.dll", "build/netstandard2.0/CodeCoverage/arm64/msdia140.dll", "build/netstandard2.0/CodeCoverage/codecoveragemessages.dll", "build/netstandard2.0/CodeCoverage/coreclr/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "build/netstandard2.0/CodeCoverage/covrun32.dll", "build/netstandard2.0/CodeCoverage/msdia140.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Core.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Instrumentation.Core.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Instrumentation.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Interprocess.dll", "build/netstandard2.0/Microsoft.CodeCoverage.props", "build/netstandard2.0/Microsoft.CodeCoverage.targets", "build/netstandard2.0/Microsoft.DiaSymReader.dll", "build/netstandard2.0/Microsoft.VisualStudio.TraceDataCollector.dll", "build/netstandard2.0/Mono.Cecil.Pdb.dll", "build/netstandard2.0/Mono.Cecil.Rocks.dll", "build/netstandard2.0/Mono.Cecil.dll", "build/netstandard2.0/ThirdPartyNotices.txt", "build/netstandard2.0/alpine/x64/Cov_x64.config", "build/netstandard2.0/alpine/x64/libCoverageInstrumentationMethod.so", "build/netstandard2.0/alpine/x64/libInstrumentationEngine.so", "build/netstandard2.0/arm64/MicrosoftInstrumentationEngine_arm64.dll", "build/netstandard2.0/cs/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/de/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/es/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/fr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/it/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ja/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ko/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/macos/x64/Cov_x64.config", "build/netstandard2.0/macos/x64/libCoverageInstrumentationMethod.dylib", "build/netstandard2.0/macos/x64/libInstrumentationEngine.dylib", "build/netstandard2.0/pl/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/pt-BR/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ru/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/tr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ubuntu/x64/Cov_x64.config", "build/netstandard2.0/ubuntu/x64/libCoverageInstrumentationMethod.so", "build/netstandard2.0/ubuntu/x64/libInstrumentationEngine.so", "build/netstandard2.0/x64/MicrosoftInstrumentationEngine_x64.dll", "build/netstandard2.0/x86/MicrosoftInstrumentationEngine_x86.dll", "build/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "lib/net462/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "microsoft.codecoverage.17.12.0.nupkg.sha512", "microsoft.codecoverage.nuspec"]}, "Microsoft.NET.Test.Sdk/17.12.0": {"sha512": "kt/PKBZ91rFCWxVIJZSgVLk+YR+4KxTuHf799ho8WNiK5ZQpJNAEZCAWX86vcKrs+DiYjiibpYKdGZP6+/N17w==", "type": "package", "path": "microsoft.net.test.sdk/17.12.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/net462/Microsoft.NET.Test.Sdk.props", "build/net462/Microsoft.NET.Test.Sdk.targets", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.cs", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.fs", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.vb", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.props", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.targets", "buildMultiTargeting/Microsoft.NET.Test.Sdk.props", "lib/net462/_._", "lib/netcoreapp3.1/_._", "microsoft.net.test.sdk.17.12.0.nupkg.sha512", "microsoft.net.test.sdk.nuspec"]}, "Microsoft.Testing.Extensions.Telemetry/1.4.3": {"sha512": "dh8jnqWikxQXJ4kWy8B82PtSAlQCnvDKh1128arDmSW5OU5xWA84HwruV3TanXi3ZjIHn1wWFCgtMOhcDNwBow==", "type": "package", "path": "microsoft.testing.extensions.telemetry/1.4.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net6.0/Microsoft.Testing.Extensions.Telemetry.props", "build/net7.0/Microsoft.Testing.Extensions.Telemetry.props", "build/net8.0/Microsoft.Testing.Extensions.Telemetry.props", "build/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.props", "buildMultiTargeting/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/net6.0/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/net7.0/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/net8.0/Microsoft.Testing.Extensions.Telemetry.props", "buildTransitive/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.props", "lib/net6.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/net6.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/net6.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/net7.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/net7.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/net8.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/net8.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.Telemetry.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Testing.Extensions.Telemetry.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll", "microsoft.testing.extensions.telemetry.1.4.3.nupkg.sha512", "microsoft.testing.extensions.telemetry.nuspec"]}, "Microsoft.Testing.Extensions.TrxReport.Abstractions/1.4.3": {"sha512": "16sWznD6ZMok/zgW+vrO6zerCFMD9N+ey9bi1iV/e9xxsQb4V4y/aW6cY/Y7E9jA7pc+aZ6ffZby43yxQOoYZA==", "type": "package", "path": "microsoft.testing.extensions.trxreport.abstractions/1.4.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/net6.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "lib/net7.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/net7.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "lib/net8.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/net8.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "lib/netstandard2.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.xml", "microsoft.testing.extensions.trxreport.abstractions.1.4.3.nupkg.sha512", "microsoft.testing.extensions.trxreport.abstractions.nuspec"]}, "Microsoft.Testing.Extensions.VSTestBridge/1.4.3": {"sha512": "xZ6oyNYh2aM5Wb+HJAy1fj2C4CNRVhINXHCjlWs/2C8hEIpdqVSpP3y6HWUN40KpFqyGD4myHGR1Rflm28UpcQ==", "type": "package", "path": "microsoft.testing.extensions.vstestbridge/1.4.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/net6.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/net6.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/net7.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/net7.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/net8.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/net8.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.VSTestBridge.dll", "lib/netstandard2.0/Microsoft.Testing.Extensions.VSTestBridge.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll", "microsoft.testing.extensions.vstestbridge.1.4.3.nupkg.sha512", "microsoft.testing.extensions.vstestbridge.nuspec"]}, "Microsoft.Testing.Platform/1.4.3": {"sha512": "NedIbwl1T7+ZMeg7gwk0Db8/RFLf0siyVpeTcRMMOle6Xl/ujaYOM4Aduo8rEfVqNj3kcQ7blegpyT3dHi+0PA==", "type": "package", "path": "microsoft.testing.platform/1.4.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net6.0/Microsoft.Testing.Platform.props", "build/net7.0/Microsoft.Testing.Platform.props", "build/net8.0/Microsoft.Testing.Platform.props", "build/netstandard2.0/Microsoft.Testing.Platform.props", "buildMultiTargeting/Microsoft.Testing.Platform.props", "buildTransitive/net6.0/Microsoft.Testing.Platform.props", "buildTransitive/net7.0/Microsoft.Testing.Platform.props", "buildTransitive/net8.0/Microsoft.Testing.Platform.props", "buildTransitive/netstandard2.0/Microsoft.Testing.Platform.props", "lib/net6.0/Microsoft.Testing.Platform.dll", "lib/net6.0/Microsoft.Testing.Platform.xml", "lib/net6.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/de/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/es/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/it/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/Microsoft.Testing.Platform.dll", "lib/net7.0/Microsoft.Testing.Platform.xml", "lib/net7.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/de/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/es/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/it/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/Microsoft.Testing.Platform.dll", "lib/net8.0/Microsoft.Testing.Platform.xml", "lib/net8.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/de/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/es/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/it/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Platform.dll", "lib/netstandard2.0/Microsoft.Testing.Platform.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/zh-Hans/Microsoft.Testing.Platform.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Platform.resources.dll", "microsoft.testing.platform.1.4.3.nupkg.sha512", "microsoft.testing.platform.nuspec"]}, "Microsoft.Testing.Platform.MSBuild/1.4.3": {"sha512": "1gGqgHtiZ6tZn/6Tby+qlKpNe5Ye/5LnxlSsyl4XMZ4m4V+Cu1K1m+gD1zxoxHIvLjgX8mCnQRK95MGBBFuumw==", "type": "package", "path": "microsoft.testing.platform.msbuild/1.4.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "_MSBuildTasks/netstandard2.0/Microsoft.Testing.Platform.MSBuild.dll", "_MSBuildTasks/netstandard2.0/Microsoft.Testing.Platform.dll", "build/net6.0/Microsoft.Testing.Platform.MSBuild.props", "build/net6.0/Microsoft.Testing.Platform.MSBuild.targets", "build/net7.0/Microsoft.Testing.Platform.MSBuild.props", "build/net7.0/Microsoft.Testing.Platform.MSBuild.targets", "build/net8.0/Microsoft.Testing.Platform.MSBuild.props", "build/net8.0/Microsoft.Testing.Platform.MSBuild.targets", "build/netstandard2.0/Microsoft.Testing.Platform.MSBuild.props", "build/netstandard2.0/Microsoft.Testing.Platform.MSBuild.targets", "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.VSTest.targets", "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.props", "buildMultiTargeting/Microsoft.Testing.Platform.MSBuild.targets", "buildTransitive/net6.0/Microsoft.Testing.Platform.MSBuild.props", "buildTransitive/net6.0/Microsoft.Testing.Platform.MSBuild.targets", "buildTransitive/net7.0/Microsoft.Testing.Platform.MSBuild.props", "buildTransitive/net7.0/Microsoft.Testing.Platform.MSBuild.targets", "buildTransitive/net8.0/Microsoft.Testing.Platform.MSBuild.props", "buildTransitive/net8.0/Microsoft.Testing.Platform.MSBuild.targets", "buildTransitive/netstandard2.0/Microsoft.Testing.Platform.MSBuild.props", "buildTransitive/netstandard2.0/Microsoft.Testing.Platform.MSBuild.targets", "lib/net6.0/Microsoft.Testing.Platform.MSBuild.dll", "lib/net6.0/Microsoft.Testing.Platform.MSBuild.xml", "lib/net6.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/zh-Hans/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net6.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/Microsoft.Testing.Platform.MSBuild.dll", "lib/net7.0/Microsoft.Testing.Platform.MSBuild.xml", "lib/net7.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/zh-Hans/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net7.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/Microsoft.Testing.Platform.MSBuild.dll", "lib/net8.0/Microsoft.Testing.Platform.MSBuild.xml", "lib/net8.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/Microsoft.Testing.Platform.MSBuild.dll", "lib/netstandard2.0/Microsoft.Testing.Platform.MSBuild.xml", "lib/netstandard2.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Testing.Platform.MSBuild.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll", "microsoft.testing.platform.msbuild.1.4.3.nupkg.sha512", "microsoft.testing.platform.msbuild.nuspec"]}, "Microsoft.TestPlatform.ObjectModel/17.12.0": {"sha512": "TDqkTKLfQuAaPcEb3pDDWnh7b3SyZF+/W9OZvWFp6eJCIiiYFdSB6taE2I6tWrFw5ywhzOb6sreoGJTI6m3rSQ==", "type": "package", "path": "microsoft.testplatform.objectmodel/17.12.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net462/Microsoft.TestPlatform.CoreUtilities.dll", "lib/net462/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/net462/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netstandard2.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netstandard2.0/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "microsoft.testplatform.objectmodel.17.12.0.nupkg.sha512", "microsoft.testplatform.objectmodel.nuspec"]}, "Microsoft.TestPlatform.TestHost/17.12.0": {"sha512": "MiPEJQNyADfwZ4pJNpQex+t9/jOClBGMiCiVVFuELCMSX2nmNfvUor3uFVxNNCg30uxDP8JDYfPnMXQzsfzYyg==", "type": "package", "path": "microsoft.testplatform.testhost/17.12.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.txt", "build/netcoreapp3.1/Microsoft.TestPlatform.TestHost.props", "build/netcoreapp3.1/x64/testhost.dll", "build/netcoreapp3.1/x64/testhost.exe", "build/netcoreapp3.1/x86/testhost.x86.dll", "build/netcoreapp3.1/x86/testhost.x86.exe", "lib/net462/_._", "lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/testhost.deps.json", "lib/netcoreapp3.1/testhost.dll", "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/x64/msdia140.dll", "lib/netcoreapp3.1/x86/msdia140.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "microsoft.testplatform.testhost.17.12.0.nupkg.sha512", "microsoft.testplatform.testhost.nuspec"]}, "MSTest/3.6.4": {"sha512": "PDBqb7FT15DBD/LQtAr2Eq/UY9YVTgsY7CD7ZiDnamc/RI+/2VSak6qotTV+x2oyhcRJxE4USRgyqXIRlyU3kw==", "type": "package", "path": "mstest/3.6.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "mstest.3.6.4.nupkg.sha512", "mstest.nuspec"]}, "MSTest.Analyzers/3.6.4": {"sha512": "4gU/VdItLebmE2+UkOaqffVmVa/in0VeIF9fmN/fG0tj5AHAasjasJcZa9U2uXBNX03cKCWlgWenlhKLz343NQ==", "type": "package", "path": "mstest.analyzers/3.6.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "analyzers/dotnet/cs/MSTest.Analyzers.CodeFixes.dll", "analyzers/dotnet/cs/MSTest.Analyzers.dll", "mstest.analyzers.3.6.4.nupkg.sha512", "mstest.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "MSTest.TestAdapter/3.6.4": {"sha512": "YdwseRA+nDhRqD2oPHjCE4KzLEN5B10A61lOslE3N3OvUwHJ6ezyZZjYWf7mrZ8jckCcx/UlBclTzgWUpMpPQw==", "type": "package", "path": "mstest.testadapter/3.6.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/net462/MSTest.TestAdapter.props", "build/net462/MSTest.TestAdapter.targets", "build/net462/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/net6.0/MSTest.TestAdapter.props", "build/net6.0/MSTest.TestAdapter.targets", "build/net6.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net7.0/MSTest.TestAdapter.props", "build/net7.0/MSTest.TestAdapter.targets", "build/net7.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net7.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net7.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/net8.0/MSTest.TestAdapter.props", "build/net8.0/MSTest.TestAdapter.targets", "build/net8.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net8.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net8.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/netcoreapp3.1/MSTest.TestAdapter.props", "build/netcoreapp3.1/MSTest.TestAdapter.targets", "build/netcoreapp3.1/Microsoft.TestPlatform.AdapterUtilities.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/netstandard2.0/MSTest.TestAdapter.props", "build/netstandard2.0/MSTest.TestAdapter.targets", "build/netstandard2.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/uap10.0/MSTest.TestAdapter.props", "build/uap10.0/MSTest.TestAdapter.targets", "build/uap10.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "mstest.testadapter.3.6.4.nupkg.sha512", "mstest.testadapter.nuspec"]}, "MSTest.TestFramework/3.6.4": {"sha512": "3nV+2CJluKmiJpCSqQfXu5idCq35+vqFywjScyauTIz0Zk7KJw7Qpzv8gtwow0To7pxIlIvwkq9rbMB+V6eOow==", "type": "package", "path": "mstest.testframework/3.6.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net6.0/MSTest.TestFramework.targets", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net7.0/MSTest.TestFramework.targets", "build/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net8.0/MSTest.TestFramework.targets", "build/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net6.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net7.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net7.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net7.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net8.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net8.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "mstest.testframework.3.6.4.nupkg.sha512", "mstest.testframework.nuspec"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "System.Diagnostics.DiagnosticSource/5.0.0": {"sha512": "tCQTzPsGZh/A9LhhA6zrqCRV4hOHsK90/G7q3Khxmn6tnB1PuNU0cRaKANP2AWcF9bn0zsuOoZOSrHuJk6oNBA==", "type": "package", "path": "system.diagnostics.diagnosticsource/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Diagnostics.DiagnosticSource.dll", "lib/net45/System.Diagnostics.DiagnosticSource.xml", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.5.0.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Metadata/1.6.0": {"sha512": "COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "type": "package", "path": "system.reflection.metadata/1.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml", "system.reflection.metadata.1.6.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["MSTest >= 3.6.4", "Microsoft.NET.Test.Sdk >= 17.12.0"]}, "packageFolders": {"/Users/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/test-group-project/MyApp.Tests/MyApp.Tests.csproj", "projectName": "MyApp.Tests", "projectPath": "/Users/<USER>/Desktop/test-group-project/MyApp.Tests/MyApp.Tests.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/test-group-project/MyApp.Tests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/opt/homebrew/Cellar/dotnet/9.0.8/libexec/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MSTest": {"target": "Package", "version": "[3.6.4, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/opt/homebrew/Cellar/dotnet/9.0.8/libexec/sdk/9.0.109/PortableRuntimeIdentifierGraph.json"}}}}