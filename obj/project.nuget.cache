{"version": 2, "dgSpecHash": "Ns3iIX3KClU=", "success": true, "projectFilePath": "/Users/<USER>/Desktop/test-group-project/MyApp.Tests/MyApp.Tests.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/microsoft.applicationinsights/2.22.0/microsoft.applicationinsights.2.22.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codecoverage/17.12.0/microsoft.codecoverage.17.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.test.sdk/17.12.0/microsoft.net.test.sdk.17.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testing.extensions.telemetry/1.4.3/microsoft.testing.extensions.telemetry.1.4.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testing.extensions.trxreport.abstractions/1.4.3/microsoft.testing.extensions.trxreport.abstractions.1.4.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testing.extensions.vstestbridge/1.4.3/microsoft.testing.extensions.vstestbridge.1.4.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testing.platform/1.4.3/microsoft.testing.platform.1.4.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testing.platform.msbuild/1.4.3/microsoft.testing.platform.msbuild.1.4.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.objectmodel/17.12.0/microsoft.testplatform.objectmodel.17.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.testhost/17.12.0/microsoft.testplatform.testhost.17.12.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/mstest/3.6.4/mstest.3.6.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/mstest.analyzers/3.6.4/mstest.analyzers.3.6.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/mstest.testadapter/3.6.4/mstest.testadapter.3.6.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/mstest.testframework/3.6.4/mstest.testframework.3.6.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.1/newtonsoft.json.13.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/5.0.0/system.diagnostics.diagnosticsource.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.metadata/1.6.0/system.reflection.metadata.1.6.0.nupkg.sha512"], "logs": []}