using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyApp;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace MyApp.Tests
{
    [TestClass]
    public class UTLab03
    {
        private readonly MethodLibrary.MethodLibrary obj = new();

        public static IEnumerable<object[]> TestCases()
        {
            var path = Path.Combine(AppContext.BaseDirectory, "TestData", "testdata.csv");

            return File.ReadAllLines(path)
                       .Skip(1)
                       .Select(line =>
                       {
                           var parts = line.Split(',');
                           return new object[]
                           {
                               int.Parse(parts[0].Trim()),
                               int.Parse(parts[1].Trim()),
                               double.Parse(parts[2].Trim())
                           };
                       });
        }

        [DataTestMethod]
        [DynamicData(nameof(TestCases), DynamicDataSourceType.Method)]
        public void TestTinhTienDien(int chiSoCu, int chiSoMoi, double expected)
        {
            double actual = Math.Round(obj.TinhTien<PERSON>ien(chiSoCu, chiSoMoi));
            Assert.AreEqual(expected, actual, "Kết quả tính tiền điện không đúng.");
        }
    }
}
